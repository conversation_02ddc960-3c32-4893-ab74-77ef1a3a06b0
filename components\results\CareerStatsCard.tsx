'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { AssessmentScores } from '../../types/assessment-results';
import { TrendingUp, Target, Award, Users } from 'lucide-react';

interface CareerStatsCardProps {
  scores: AssessmentScores;
}

export default function CareerStatsCard({ scores }: CareerStatsCardProps) {
  // Calculate career-focused statistics matching the radar chart
  const riasecScores = [
    { name: 'Development', score: scores.riasec.investigative, category: 'Technical' },
    { name: 'Design', score: scores.riasec.artistic, category: 'Creative' },
    { name: 'Management', score: scores.riasec.enterprising, category: 'Leadership' },
    { name: 'Sales', score: Math.round((scores.riasec.enterprising + scores.riasec.social) / 2), category: 'Business' },
    { name: 'Support', score: scores.riasec.social, category: 'Social' },
    { name: 'Administration', score: scores.riasec.conventional, category: 'Operational' },
  ];

  const topSkill = riasecScores.reduce((prev, current) => 
    prev.score > current.score ? prev : current
  );

  const averageScore = Math.round(
    riasecScores.reduce((sum, skill) => sum + skill.score, 0) / riasecScores.length
  );

  const getScoreLevel = (score: number) => {
    if (score >= 80) return { label: 'Expert', color: '#22c55e' };
    if (score >= 60) return { label: 'Advanced', color: '#3b82f6' };
    if (score >= 40) return { label: 'Intermediate', color: '#eab308' };
    if (score >= 20) return { label: 'Beginner', color: '#f97316' };
    return { label: 'Novice', color: '#ef4444' };
  };

  const topSkillLevel = getScoreLevel(topSkill.score);
  const overallLevel = getScoreLevel(averageScore);

  // Calculate skill distribution
  const skillDistribution = {
    technical: scores.riasec.investigative + scores.riasec.realistic,
    creative: scores.riasec.artistic,
    social: scores.riasec.social,
    business: scores.riasec.enterprising,
    operational: scores.riasec.conventional,
  };

  const maxDistribution = Math.max(...Object.values(skillDistribution));
  const dominantArea = Object.entries(skillDistribution).find(
    ([_, value]) => value === maxDistribution
  )?.[0] || 'technical';

  return (
    <Card className="bg-white border-gray-200/60 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-[#4f46e5]/10 rounded-lg">
            <TrendingUp className="w-5 h-5 text-[#4f46e5]" />
          </div>
          <div>
            <CardTitle className="text-lg font-semibold text-[#1f2937]">
              Performance Insights
            </CardTitle>
            <p className="text-xs text-[#6b7280]">Detailed analysis of your career competencies</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Top Skill */}
        <div className="p-4 bg-gradient-to-r from-[#22c55e]/10 to-[#16a34a]/10 rounded-lg border border-[#22c55e]/20">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4 text-[#22c55e]" />
              <span className="text-sm font-medium text-[#1e1e1e]">Top Skill</span>
            </div>
            <Badge 
              style={{ backgroundColor: topSkillLevel.color + '20', color: topSkillLevel.color }}
              className="text-xs font-medium"
            >
              {topSkillLevel.label}
            </Badge>
          </div>
          <div className="text-lg font-bold text-[#22c55e]">{topSkill.name}</div>
          <div className="text-sm text-[#64707d]">Score: {topSkill.score}/100</div>
        </div>

        {/* Overall Performance */}
        <div className="p-4 bg-gradient-to-r from-[#6475e9]/10 to-[#5a6bd8]/10 rounded-lg border border-[#6475e9]/20">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-[#6475e9]" />
              <span className="text-sm font-medium text-[#1e1e1e]">Overall Level</span>
            </div>
            <Badge 
              style={{ backgroundColor: overallLevel.color + '20', color: overallLevel.color }}
              className="text-xs font-medium"
            >
              {overallLevel.label}
            </Badge>
          </div>
          <div className="text-lg font-bold text-[#6475e9]">{averageScore}/100</div>
          <div className="text-sm text-[#64707d]">Average across all skills</div>
        </div>

        {/* Dominant Area */}
        <div className="p-4 bg-gradient-to-r from-[#f59e0b]/10 to-[#d97706]/10 rounded-lg border border-[#f59e0b]/20">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-4 h-4 text-[#f59e0b]" />
            <span className="text-sm font-medium text-[#1e1e1e]">Dominant Area</span>
          </div>
          <div className="text-lg font-bold text-[#f59e0b] capitalize">{dominantArea}</div>
          <div className="text-sm text-[#64707d]">
            {dominantArea === 'technical' && 'Problem-solving & Analysis'}
            {dominantArea === 'creative' && 'Design & Innovation'}
            {dominantArea === 'social' && 'Communication & Teamwork'}
            {dominantArea === 'business' && 'Leadership & Strategy'}
            {dominantArea === 'operational' && 'Organization & Process'}
          </div>
        </div>

        {/* Skills Breakdown */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-[#1e1e1e]">Skills Breakdown</h4>
          {riasecScores.map((skill, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm text-[#64707d]">{skill.name}</span>
              <div className="flex items-center gap-2">
                <div className="w-16 h-2 bg-[#f3f4f6] rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-[#6475e9] rounded-full transition-all duration-300"
                    style={{ width: `${skill.score}%` }}
                  />
                </div>
                <span className="text-xs font-medium text-[#64707d] w-8 text-right">
                  {skill.score}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
