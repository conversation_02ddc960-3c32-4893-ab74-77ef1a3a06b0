<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Timeout Error Handling Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        .status.idle { background-color: #e9ecef; color: #495057; }
        .status.processing { background-color: #fff3cd; color: #856404; }
        .status.failed { background-color: #f8d7da; color: #721c24; }
        .status.completed { background-color: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Timeout Error Handling Test</h1>
        
        <div class="test-section info">
            <h3>Test Purpose</h3>
            <p>This test verifies that WebSocket timeout errors are properly handled and displayed in the AssessmentErrorScreen component.</p>
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li>When WebSocket connection times out, the workflow status should change to 'failed'</li>
                <li>The error message should be properly displayed</li>
                <li>The AssessmentErrorScreen should show appropriate error information</li>
                <li>The onError callback should be triggered</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testWebSocketTimeout()">Test WebSocket Timeout</button>
            <button onclick="testAssessmentFailed()">Test Assessment Failed Event</button>
            <button onclick="testConnectionError()">Test Connection Error</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>Current Status</h3>
            <div id="status" class="status idle">idle</div>
            <div id="message">Ready to test</div>
        </div>

        <div class="test-section">
            <h3>Test Log</h3>
            <div id="log" class="log">Test log will appear here...\n</div>
        </div>

        <div class="test-section">
            <h3>Instructions</h3>
            <ol>
                <li>Open the browser developer console to see detailed logs</li>
                <li>Click one of the test buttons above</li>
                <li>Observe the status changes and error handling</li>
                <li>Check that the workflow properly transitions to 'failed' status</li>
                <li>Verify that error callbacks are triggered</li>
            </ol>
        </div>
    </div>

    <script>
        // Mock assessment workflow for testing
        class MockAssessmentWorkflow {
            constructor(callbacks = {}) {
                this.callbacks = callbacks;
                this.state = {
                    status: 'idle',
                    progress: 0,
                    message: 'Ready to test',
                    useWebSocket: false,
                    webSocketConnected: false
                };
                this.updateUI();
            }

            updateState(updates) {
                const previousState = { ...this.state };
                this.state = { ...this.state, ...updates };
                
                this.log(`Status changed from ${previousState.status} to ${this.state.status}`);
                
                if (this.callbacks.onStatusChange) {
                    this.callbacks.onStatusChange(this.state);
                }
                
                this.updateUI();
            }

            updateUI() {
                const statusEl = document.getElementById('status');
                const messageEl = document.getElementById('message');
                
                statusEl.textContent = this.state.status;
                statusEl.className = `status ${this.state.status}`;
                messageEl.textContent = this.state.message;
            }

            log(message) {
                const logEl = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                logEl.textContent += `[${timestamp}] ${message}\n`;
                logEl.scrollTop = logEl.scrollHeight;
                console.log(`Assessment Workflow: ${message}`);
            }

            async simulateWebSocketTimeout() {
                this.log('Starting WebSocket timeout simulation...');
                
                this.updateState({
                    status: 'submitting',
                    progress: 20,
                    message: 'Submitting assessment...'
                });

                await this.delay(500);

                this.updateState({
                    status: 'queued',
                    progress: 10,
                    message: 'Assessment submitted, connecting to real-time updates...',
                    useWebSocket: true
                });

                await this.delay(1000);

                // Simulate timeout
                this.log('Simulating WebSocket connection timeout...');
                
                this.updateState({
                    status: 'failed',
                    useWebSocket: false,
                    webSocketConnected: false,
                    message: 'WebSocket connection timeout. Please check your connection and try again.'
                });

                // Call error callback
                if (this.callbacks.onError) {
                    const error = new Error('WebSocket connection timeout');
                    this.log('Calling onError callback...');
                    this.callbacks.onError(error, this.state);
                }
            }

            async simulateAssessmentFailed() {
                this.log('Starting assessment failed simulation...');
                
                this.updateState({
                    status: 'submitting',
                    progress: 20,
                    message: 'Submitting assessment...'
                });

                await this.delay(500);

                this.updateState({
                    status: 'queued',
                    progress: 10,
                    message: 'Assessment submitted, processing...',
                    useWebSocket: true,
                    webSocketConnected: true
                });

                await this.delay(1000);

                this.updateState({
                    status: 'processing',
                    progress: 50,
                    message: 'Processing assessment...'
                });

                await this.delay(1000);

                // Simulate assessment failed event
                this.log('Simulating assessment-failed WebSocket event...');
                
                const errorMessage = 'Assessment processing failed due to server error';
                this.updateState({
                    status: 'failed',
                    message: errorMessage
                });

                // Call error callback
                if (this.callbacks.onError) {
                    const error = new Error(errorMessage);
                    this.log('Calling onError callback...');
                    this.callbacks.onError(error, this.state);
                }
            }

            async simulateConnectionError() {
                this.log('Starting connection error simulation...');
                
                this.updateState({
                    status: 'submitting',
                    progress: 20,
                    message: 'Submitting assessment...'
                });

                await this.delay(500);

                // Simulate immediate connection error
                this.log('Simulating WebSocket connection error...');
                
                this.updateState({
                    status: 'failed',
                    useWebSocket: false,
                    webSocketConnected: false,
                    message: 'WebSocket error: Connection refused'
                });

                // Call error callback
                if (this.callbacks.onError) {
                    const error = new Error('Connection refused');
                    this.log('Calling onError callback...');
                    this.callbacks.onError(error, this.state);
                }
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // Create workflow instance
        const workflow = new MockAssessmentWorkflow({
            onStatusChange: (state) => {
                console.log('Status change callback:', state);
            },
            onError: (error, state) => {
                console.error('Error callback:', error, state);
                workflow.log(`ERROR: ${error.message}`);
                
                // Show error details
                const errorInfo = `
Error Type: ${error.name}
Error Message: ${error.message}
Workflow Status: ${state.status}
Workflow Message: ${state.message}
WebSocket Connected: ${state.webSocketConnected}
                `.trim();
                
                workflow.log('Error Details:\n' + errorInfo);
            }
        });

        // Test functions
        async function testWebSocketTimeout() {
            await workflow.simulateWebSocketTimeout();
        }

        async function testAssessmentFailed() {
            await workflow.simulateAssessmentFailed();
        }

        async function testConnectionError() {
            await workflow.simulateConnectionError();
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Test log cleared...\n';
        }

        // Initialize
        workflow.log('Test page loaded. Ready to test WebSocket error handling.');
    </script>
</body>
</html>
