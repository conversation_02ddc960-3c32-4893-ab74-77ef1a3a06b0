"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "../ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { Badge } from "../ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "../ui/alert-dialog"
import { ExternalLink, Trash2, Plus } from "lucide-react"
import type { AssessmentData } from "../../types/dashboard"

interface AssessmentTableProps {
  data: AssessmentData[]
  onRefresh?: () => Promise<void>
}

export function AssessmentTable({ data, onRefresh }: AssessmentTableProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [assessmentData, setAssessmentData] = useState(data)
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  // Update assessment data when prop changes
  useEffect(() => {
    setAssessmentData(data)
  }, [data])

  const totalPages = Math.ceil(assessmentData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = assessmentData.slice(startIndex, endIndex)

  const handleDelete = async (id: number) => {
    // Find the assessment item to get the resultId
    const assessmentItem = assessmentData.find(item => item.id === id);
    if (!assessmentItem?.resultId) {
      console.error('No resultId found for assessment item:', id);
      return;
    }

    setIsDeleting(assessmentItem.resultId);

    try {
      // Import API service dynamically
      const { apiService } = await import('../../services/apiService');

      // Call the API to delete the result
      await apiService.deleteResult(assessmentItem.resultId);

      console.log('Assessment deleted successfully:', assessmentItem.resultId);

      // Refresh the data if callback is provided
      if (onRefresh) {
        await onRefresh();
      } else {
        // Fallback: remove from local state
        const updatedData = assessmentData.filter(item => item.id !== id);
        setAssessmentData(updatedData);

        // If current page becomes empty after deletion, go to previous page
        const newTotalPages = Math.ceil(updatedData.length / itemsPerPage);
        if (currentPage > newTotalPages && newTotalPages > 0) {
          setCurrentPage(newTotalPages);
        }
      }
    } catch (error) {
      console.error('Error deleting assessment:', error);
      // You might want to show a toast notification here
      alert('Gagal menghapus assessment. Silakan coba lagi.');
    } finally {
      setIsDeleting(null);
    }
  }

  const handleView = (id: number) => {
    // Find the assessment item to get the correct resultId
    const assessmentItem = assessmentData.find(item => item.id === id);
    const resultId = assessmentItem?.resultId || `result-${id}`;

    // Navigate to results page with the correct result ID
    router.push(`/results/${resultId}`)
  }

  return (
    <Card className="bg-white border-[#eaecf0] h-[800px] flex flex-col">
      <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between flex-shrink-0 gap-4">
        <div className="flex-1">
          <CardTitle className="text-lg font-semibold text-[#1e1e1e]">Assessment History</CardTitle>
          <p className="text-xs text-[#64707d] mt-1">
            Review your analytics results and use this information to improve future performance.
          </p>
        </div>
        <div className="flex gap-2 w-full sm:w-auto">
          <Button
            className="bg-[#6475e9] hover:bg-[#5a6bd8] text-white text-xs flex-1 sm:flex-none min-h-[44px]"
            onClick={() => router.push("/select-assessment")}
          >
            <Plus className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">New Assessment</span>
            <span className="sm:hidden">New</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 min-h-0">
        <div className="flex-1 overflow-auto">
          <div className="overflow-x-auto">
            <Table className="min-w-[600px]">
            <TableHeader>
              <TableRow className="border-[#eaecf0]">
                <TableHead className="text-[#64707d] font-medium min-w-[60px]">No</TableHead>
                <TableHead className="text-[#64707d] font-medium min-w-[120px]">Nama</TableHead>
                <TableHead className="text-[#64707d] font-medium min-w-[100px]">Tipe</TableHead>
                <TableHead className="text-[#64707d] font-medium min-w-[100px]">Tanggal</TableHead>
                <TableHead className="text-[#64707d] font-medium min-w-[80px]">Status</TableHead>
                <TableHead className="text-[#64707d] font-medium min-w-[80px]">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((item, index) => (
                <TableRow key={item.id} className="border-[#eaecf0]">
                  <TableCell className="text-[#1e1e1e]">{startIndex + index + 1}</TableCell>
                  <TableCell className="text-[#1e1e1e] font-medium">{item.nama}</TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="bg-[#f3f3f3] text-[#64707d] text-xs">
                      {item.tipe}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-[#64707d] text-sm">{item.tanggal}</TableCell>
                  <TableCell>
                    <Badge
                      variant="secondary"
                      className={`text-xs ${
                        item.status === "Selesai"
                          ? "bg-green-100 text-green-800 border-green-200"
                          : "bg-yellow-100 text-yellow-800 border-yellow-200"
                      }`}
                    >
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-9 w-9 min-h-[44px] sm:min-h-0 sm:h-8 sm:w-8"
                        onClick={() => handleView(item.id)}
                      >
                        <ExternalLink className="w-4 h-4 text-[#64707d]" />
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-9 w-9 min-h-[44px] sm:min-h-0 sm:h-8 sm:w-8"
                            disabled={isDeleting === item.resultId}
                          >
                            <Trash2 className="w-4 h-4 text-[#64707d]" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="mx-4 max-w-[calc(100vw-2rem)] sm:max-w-lg">
                          <AlertDialogHeader>
                            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
                            <AlertDialogDescription>
                              Apakah Anda yakin ingin menghapus assessment "{item.nama}"?
                              Tindakan ini tidak dapat dibatalkan.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="flex-col sm:flex-row gap-2">
                            <AlertDialogCancel
                              disabled={isDeleting === item.resultId}
                              className="w-full sm:w-auto"
                            >
                              Batal
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDelete(item.id)}
                              className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
                              disabled={isDeleting === item.resultId}
                            >
                              {isDeleting === item.resultId ? 'Menghapus...' : 'Ya, Hapus'}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex flex-col sm:flex-row items-center justify-between pt-4 border-t border-[#eaecf0] mt-4 flex-shrink-0 gap-4">
          <div className="flex items-center gap-2 order-2 sm:order-1">
            <span className="text-sm text-[#64707d] hidden sm:inline">Show</span>
            <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
              <SelectTrigger className="w-16 h-9 min-h-[44px] sm:min-h-0 sm:h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-[#64707d]">
              <span className="hidden sm:inline">Data</span>
              <span className="sm:hidden">per halaman</span>
            </span>
          </div>

          <div className="flex gap-1 order-1 sm:order-2 flex-wrap justify-center">
            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              let page;
              if (totalPages <= 5) {
                page = i + 1;
              } else if (currentPage <= 3) {
                page = i + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + i;
              } else {
                page = currentPage - 2 + i;
              }

              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "ghost"}
                  size="sm"
                  className={`w-9 h-9 min-h-[44px] sm:min-h-0 sm:w-8 sm:h-8 ${
                    currentPage === page ? "bg-[#6475e9] hover:bg-[#5a6bd8] text-white" : "text-[#64707d]"
                  }`}
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2 text-[#64707d]">...</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-9 h-9 min-h-[44px] sm:min-h-0 sm:w-8 sm:h-8 text-[#64707d]"
                  onClick={() => setCurrentPage(totalPages)}
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

