<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test RIASEC Data Fetching</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test RIASEC Data Fetching</h1>
    
    <div class="test-section info">
        <h3>Test Setup</h3>
        <p>This page tests the RIASEC data fetching functionality for the dashboard.</p>
        <button onclick="setupTestData()">Setup Test Data in localStorage</button>
        <button onclick="clearTestData()">Clear Test Data</button>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="results"></div>
    </div>

    <script>
        function setupTestData() {
            // Add mock assessment result to localStorage
            const mockResult = {
                id: 'test-result-001',
                userId: 'current-user',
                createdAt: new Date().toISOString(),
                status: 'completed',
                assessment_data: {
                    riasec: {
                        realistic: 75,
                        investigative: 85,
                        artistic: 65,
                        social: 70,
                        enterprising: 80,
                        conventional: 60
                    },
                    ocean: {
                        openness: 88,
                        conscientiousness: 67,
                        extraversion: 45,
                        agreeableness: 72,
                        neuroticism: 25
                    },
                    viaIs: {
                        creativity: 92,
                        curiosity: 89,
                        judgment: 78
                    }
                },
                persona_profile: {
                    title: 'Test Assessment Result',
                    description: 'This is a test assessment result for RIASEC data testing.',
                    strengths: ['Analytical thinking', 'Creativity'],
                    recommendations: ['Consider technical roles'],
                    careerRecommendation: [],
                    roleModel: []
                }
            };

            localStorage.setItem('assessment-result-test-result-001', JSON.stringify(mockResult));
            
            // Add to assessment history
            const history = [{
                id: 1,
                nama: 'Test Assessment',
                tipe: 'Personality Assessment',
                tanggal: new Date().toLocaleDateString('id-ID'),
                status: 'Selesai',
                resultId: 'test-result-001'
            }];
            
            localStorage.setItem('assessment-history', JSON.stringify(history));
            
            updateResults('✅ Test data setup complete!', 'success');
        }

        function clearTestData() {
            localStorage.removeItem('assessment-result-test-result-001');
            localStorage.removeItem('assessment-history');
            updateResults('🗑️ Test data cleared!', 'info');
        }

        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="${type}"><p>${message}</p></div>`;
        }

        // Test the localStorage data
        function testLocalStorageData() {
            const history = JSON.parse(localStorage.getItem('assessment-history') || '[]');
            const result = localStorage.getItem('assessment-result-test-result-001');
            
            updateResults(`
                <h4>localStorage Test Results:</h4>
                <p><strong>Assessment History:</strong> ${history.length} items</p>
                <p><strong>Test Result Available:</strong> ${!!result}</p>
                <pre>${JSON.stringify(history, null, 2)}</pre>
                ${result ? `<pre>${JSON.stringify(JSON.parse(result).assessment_data.riasec, null, 2)}</pre>` : ''}
            `, 'info');
        }

        // Auto-test on page load
        window.onload = function() {
            testLocalStorageData();
        };
    </script>
</body>
</html>
