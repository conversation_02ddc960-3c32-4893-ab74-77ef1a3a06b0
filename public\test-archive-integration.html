<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archive API Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .step { color: #6f42c1; font-weight: bold; }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .results-table th,
        .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Archive API Integration Test</h1>
        <p>Test the integration between the frontend and Archive Service API.</p>

        <!-- Configuration Section -->
        <div class="test-section">
            <h3>⚙️ Configuration</h3>
            <div class="input-group">
                <label for="apiBaseUrl">API Base URL:</label>
                <input type="text" id="apiBaseUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
            </div>
            <div class="input-group">
                <label for="authToken">Auth Token (JWT):</label>
                <input type="text" id="authToken" placeholder="Enter your JWT token here">
            </div>
            <button class="test-button" onclick="saveConfig()">Save Configuration</button>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button class="test-button" onclick="testHealthCheck()">Test Health Check</button>
            <button class="test-button" onclick="testGetResults()">Test Get Results</button>
            <button class="test-button" onclick="testGetStats()">Test Get Statistics</button>
            <button class="test-button" onclick="testDataTransformation()">Test Data Transformation</button>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>

        <!-- Log Output -->
        <div class="test-section">
            <h3>📋 Test Log</h3>
            <div id="log" class="log"></div>
        </div>

        <!-- Results Display -->
        <div class="test-section">
            <h3>📊 Results Display</h3>
            <div id="resultsDisplay"></div>
        </div>
    </div>

    <script>
        let config = {
            apiBaseUrl: 'http://localhost:3000',
            authToken: ''
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function saveConfig() {
            config.apiBaseUrl = document.getElementById('apiBaseUrl').value;
            config.authToken = document.getElementById('authToken').value;
            log('✅ Configuration saved', 'success');
            
            if (!config.authToken) {
                log('⚠️ Warning: No auth token provided. Some tests may fail.', 'warning');
            }
        }

        async function makeApiRequest(endpoint, options = {}) {
            const url = `${config.apiBaseUrl}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (config.authToken) {
                headers['Authorization'] = `Bearer ${config.authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testHealthCheck() {
            log('🏥 Testing Archive Service Health Check...', 'step');
            
            try {
                const result = await makeApiRequest('/api/archive/health');
                
                if (result.success) {
                    log('✅ Health check successful', 'success');
                    log(`📊 Service: ${result.data.service}`, 'info');
                    log(`📊 Status: ${result.data.status}`, 'info');
                    log(`📊 Version: ${result.data.version}`, 'info');
                } else {
                    log(`❌ Health check failed: ${result.status}`, 'error');
                    log(`📄 Response: ${JSON.stringify(result.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testGetResults() {
            log('📊 Testing Get User Results...', 'step');
            
            if (!config.authToken) {
                log('❌ Auth token required for this test', 'error');
                return;
            }

            try {
                const result = await makeApiRequest('/api/archive/results?limit=5&sort=created_at&order=DESC');
                
                if (result.success && result.data.success) {
                    log('✅ Get results successful', 'success');
                    log(`📊 Results count: ${result.data.data.results.length}`, 'info');
                    log(`📊 Total: ${result.data.data.pagination?.total || 'N/A'}`, 'info');
                    
                    displayResults(result.data.data.results);
                } else {
                    log(`❌ Get results failed: ${result.status}`, 'error');
                    log(`📄 Response: ${JSON.stringify(result.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Get results error: ${error.message}`, 'error');
            }
        }

        async function testGetStats() {
            log('📈 Testing Get User Statistics...', 'step');
            
            if (!config.authToken) {
                log('❌ Auth token required for this test', 'error');
                return;
            }

            try {
                const result = await makeApiRequest('/api/archive/stats');
                
                if (result.success && result.data.success) {
                    log('✅ Get statistics successful', 'success');
                    log(`📊 Total results: ${result.data.data.total_results}`, 'info');
                    log(`📊 Completed assessments: ${result.data.data.completed_assessments}`, 'info');
                    log(`📊 Success rate: ${(result.data.data.success_rate * 100).toFixed(1)}%`, 'info');
                } else {
                    log(`❌ Get statistics failed: ${result.status}`, 'error');
                    log(`📄 Response: ${JSON.stringify(result.data)}`, 'error');
                }
            } catch (error) {
                log(`❌ Get statistics error: ${error.message}`, 'error');
            }
        }

        function testDataTransformation() {
            log('🔄 Testing Data Transformation...', 'step');
            
            const mockApiResponse = {
                success: true,
                data: {
                    results: [
                        {
                            id: 'test-uuid-123',
                            user_id: 'user-456',
                            assessment_name: 'AI-Driven Talent Mapping',
                            status: 'completed',
                            created_at: '2024-01-15T10:30:00.000Z',
                            persona_profile: {
                                archetype: 'The Analytical Innovator'
                            }
                        },
                        {
                            id: 'test-uuid-456',
                            user_id: 'user-456',
                            assessment_name: 'AI-Driven Talent Mapping',
                            status: 'processing',
                            created_at: '2024-01-14T15:20:00.000Z',
                            persona_profile: {
                                archetype: 'The Creative Collaborator'
                            }
                        }
                    ]
                }
            };

            try {
                // Transform data as the frontend would
                const transformedData = mockApiResponse.data.results.map((result, index) => ({
                    id: index + 1,
                    nama: result.persona_profile?.archetype || result.assessment_name || 'Assessment Result',
                    tipe: "Personality Assessment",
                    tanggal: new Date(result.created_at).toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    }),
                    status: result.status === 'completed' ? "Selesai" : "Belum Selesai",
                    resultId: result.id
                }));

                log('✅ Data transformation successful', 'success');
                log(`📊 Transformed ${transformedData.length} items`, 'info');
                log(`📄 Sample: ${JSON.stringify(transformedData[0], null, 2)}`, 'info');
                
                displayTransformedData(transformedData);
            } catch (error) {
                log(`❌ Data transformation error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            log('🚀 Running all tests...', 'step');
            clearLog();
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testGetResults();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testGetStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testDataTransformation();
            
            log('✅ All tests completed!', 'success');
        }

        function displayResults(results) {
            const display = document.getElementById('resultsDisplay');
            
            if (!results || results.length === 0) {
                display.innerHTML = '<p>No results to display</p>';
                return;
            }

            let html = '<h4>📊 API Results</h4>';
            html += '<table class="results-table">';
            html += '<tr><th>ID</th><th>Assessment Name</th><th>Status</th><th>Archetype</th><th>Created At</th></tr>';
            
            results.forEach(result => {
                html += `<tr>
                    <td>${result.id}</td>
                    <td>${result.assessment_name}</td>
                    <td>${result.status}</td>
                    <td>${result.persona_profile?.archetype || 'N/A'}</td>
                    <td>${new Date(result.created_at).toLocaleString()}</td>
                </tr>`;
            });
            
            html += '</table>';
            display.innerHTML = html;
        }

        function displayTransformedData(data) {
            const display = document.getElementById('resultsDisplay');
            
            let html = '<h4>🔄 Transformed Data (Frontend Format)</h4>';
            html += '<table class="results-table">';
            html += '<tr><th>ID</th><th>Nama</th><th>Tipe</th><th>Tanggal</th><th>Status</th><th>Result ID</th></tr>';
            
            data.forEach(item => {
                html += `<tr>
                    <td>${item.id}</td>
                    <td>${item.nama}</td>
                    <td>${item.tipe}</td>
                    <td>${item.tanggal}</td>
                    <td>${item.status}</td>
                    <td>${item.resultId}</td>
                </tr>`;
            });
            
            html += '</table>';
            display.innerHTML = html;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Archive API Integration Test initialized', 'success');
            log('📝 Instructions:', 'info');
            log('1. Enter your API base URL and JWT token', 'info');
            log('2. Click "Save Configuration"', 'info');
            log('3. Run individual tests or "Run All Tests"', 'info');
            log('4. Check the results in the log and results display', 'info');
        });
    </script>
</body>
</html>
