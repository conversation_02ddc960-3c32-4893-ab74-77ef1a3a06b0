import { Card, CardContent } from "../ui/card"
import { Progress } from "../ui/progress"
import type { ProgressItem } from "../../types/dashboard"

interface ProgressCardProps {
  title: string
  description: string
  data: ProgressItem[]
}

export function ProgressCard({ title, description, data }: ProgressCardProps) {
  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardContent className="p-4 md:p-6">
        <h3 className="text-base md:text-lg font-semibold text-[#1e1e1e]">{title}</h3>
        <p className="text-xs text-[#64707d] mb-3 md:mb-2 line-clamp-2">{description}</p>

        <div className="space-y-3 md:space-y-4">
          {data.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-[#64707d] truncate flex-1 mr-2">{item.label}</span>
                <span className="text-[#1e1e1e] font-medium flex-shrink-0">{item.value}%</span>
              </div>
              <Progress
                value={item.value}
                className="h-2 md:h-2 bg-[#eaecf0]"
                style={{
                  "--progress-background": "#6475e9",
                } as React.CSSProperties}
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
