<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket-Only Assessment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(34, 197, 94, 0.2); border: 2px solid #22c55e; }
        .error { background: rgba(239, 68, 68, 0.2); border: 2px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border: 2px solid #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.2); border: 2px solid #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #6b7280; cursor: not-allowed; }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 WebSocket-Only Assessment Test</h1>
        <p>Test untuk memverifikasi implementasi WebSocket-only pada sistem assessment.</p>

        <div id="websocket-status" class="status info">
            🔄 WebSocket Status: Checking...
        </div>

        <div id="browser-support" class="status info">
            🌐 Browser Support: Checking...
        </div>

        <div class="controls">
            <button onclick="testWebSocketSupport()">Test WebSocket Support</button>
            <button onclick="testConnectionToMockServer()">Test Mock Server Connection</button>
            <button onclick="simulateAssessmentFlow()">Simulate Assessment Flow</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div id="test-logs" class="log">
            <div>📋 Test logs will appear here...</div>
        </div>

        <div class="info-section">
            <h3>🧪 Test Scenarios</h3>
            <ul>
                <li><strong>WebSocket Support:</strong> Checks if browser supports WebSocket</li>
                <li><strong>Mock Server:</strong> Tests connection to mock WebSocket server</li>
                <li><strong>Assessment Flow:</strong> Simulates the full assessment workflow</li>
            </ul>

            <h3>📋 Expected Results</h3>
            <ul>
                <li>✅ <strong>With Mock Server:</strong> WebSocket connection successful</li>
                <li>❌ <strong>Without Mock Server:</strong> Clear error message, no fallback</li>
                <li>🔄 <strong>Assessment Flow:</strong> Real-time updates via WebSocket only</li>
            </ul>

            <h3>🚀 How to Test</h3>
            <ol>
                <li>Start mock server: <code>node mock-websocket-server.js</code></li>
                <li>Click "Test Mock Server Connection"</li>
                <li>Click "Simulate Assessment Flow"</li>
                <li>Stop mock server and repeat to test error handling</li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('test-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#ef4444' : 
                                 type === 'success' ? '#22c55e' : 
                                 type === 'warning' ? '#f59e0b' : '#ffffff';
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function testWebSocketSupport() {
            log('🧪 Testing WebSocket support...', 'info');
            
            if (typeof WebSocket !== 'undefined') {
                log('✅ WebSocket is supported in this browser', 'success');
                updateStatus('browser-support', '✅ Browser Support: WebSocket Supported', 'success');
                return true;
            } else {
                log('❌ WebSocket is NOT supported in this browser', 'error');
                updateStatus('browser-support', '❌ Browser Support: WebSocket NOT Supported', 'error');
                return false;
            }
        }

        function testConnectionToMockServer() {
            if (!testWebSocketSupport()) {
                log('❌ Cannot test connection - WebSocket not supported', 'error');
                return;
            }

            log('🔌 Attempting to connect to mock WebSocket server...', 'info');
            updateStatus('websocket-status', '🔄 WebSocket Status: Connecting...', 'warning');

            const ws = new WebSocket('ws://localhost:3002');
            
            ws.onopen = function() {
                log('✅ Successfully connected to mock WebSocket server', 'success');
                updateStatus('websocket-status', '✅ WebSocket Status: Connected to Mock Server', 'success');
                ws.close();
            };

            ws.onerror = function(error) {
                log('❌ Failed to connect to mock WebSocket server', 'error');
                log('💡 Make sure to run: node mock-websocket-server.js', 'warning');
                updateStatus('websocket-status', '❌ WebSocket Status: Connection Failed', 'error');
            };

            ws.onclose = function() {
                log('🔌 WebSocket connection closed', 'info');
            };
        }

        function simulateAssessmentFlow() {
            if (!testWebSocketSupport()) {
                log('❌ Cannot simulate assessment - WebSocket not supported', 'error');
                return;
            }

            log('🎯 Simulating WebSocket-only assessment flow...', 'info');
            
            // This simulates what the actual assessment workflow would do
            const ws = new WebSocket('ws://localhost:3002');
            
            ws.onopen = function() {
                log('✅ WebSocket connection established for assessment', 'success');
                
                // Simulate authentication
                setTimeout(() => {
                    log('🔐 Authenticating with WebSocket server...', 'info');
                    ws.send(JSON.stringify({ type: 'authenticate', token: 'test-token' }));
                }, 500);
                
                // Simulate assessment submission
                setTimeout(() => {
                    log('📝 Submitting assessment via WebSocket...', 'info');
                    ws.send(JSON.stringify({ 
                        type: 'submit-assessment', 
                        data: { answers: { 1: 4, 2: 3, 3: 5 } }
                    }));
                }, 1500);
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                log(`📨 Received: ${data.type} - ${data.message || 'No message'}`, 'success');
                
                if (data.type === 'assessment-completed') {
                    log('🎉 Assessment completed successfully via WebSocket!', 'success');
                    ws.close();
                }
            };

            ws.onerror = function(error) {
                log('❌ WebSocket assessment flow failed', 'error');
                log('💡 This demonstrates the WebSocket-only approach - no fallback to polling', 'warning');
                log('🔧 User would see clear error message with retry options', 'info');
            };

            ws.onclose = function() {
                log('🔌 Assessment WebSocket connection closed', 'info');
            };
        }

        function clearLogs() {
            document.getElementById('test-logs').innerHTML = '<div>📋 Test logs cleared...</div>';
        }

        // Initialize on page load
        window.onload = function() {
            log('🚀 WebSocket-Only Assessment Test initialized', 'info');
            testWebSocketSupport();
        };
    </script>
</body>
</html>
