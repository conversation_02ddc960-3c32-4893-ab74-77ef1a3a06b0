import { Card, CardContent } from "../ui/card"
import { OceanScores } from "../../types/assessment-results"

interface OceanCardProps {
  oceanScores?: OceanScores
}

export function OceanCard({ oceanScores }: OceanCardProps) {
  // Default Ocean scores if no assessment data is available
  const defaultScores: OceanScores = {
    openness: 75,
    conscientiousness: 60,
    extraversion: 45,
    agreeableness: 80,
    neuroticism: 25
  }

  // Use provided scores or fallback to defaults
  const scores = oceanScores || defaultScores

  // Ocean data for bar chart with colors matching the current design
  const oceanData = [
    {
      trait: 'OPNS',
      score: scores.openness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'CONS',
      score: scores.conscientiousness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'EXTN',
      score: scores.extraversion,
      color: '#6475e9', // Blue
    },
    {
      trait: 'AGRS',
      score: scores.agreeableness,
      color: '#6475e9', // Blue
    },
    {
      trait: 'NESM',
      score: scores.neuroticism,
      color: '#a2acf2', // Light blue for neuroticism (as in original)
    },
  ]

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardContent className="flex flex-col space-y-1.5 p-4 md:p-6">
        {/* OCEAN Header */}
        <div className="text-center mb-0">
          <h3 className="text-base md:text-lg font-semibold text-[#1e1e1e] text-left">OCEAN Assessment</h3>
          <p className="text-xs text-[#64707d] text-left">Big Five Personality Traits</p>
        </div>

        {/* OCEAN Bar Chart */}
        <div className="flex items-center justify-center gap-1 md:gap-2">
          {oceanData.map((item, index) => {
            const heightPercentage = Math.max((item.score / 100) * 100, 15) // Minimum 15% height for visibility
            return (
              <div key={item.trait} className="flex flex-col items-center gap-1 flex-1">
                {/* Percentage label positioned above the bar chart area */}
                <div className="h-5 md:h-6 flex items-center justify-center">
                  <span className="text-xs font-semibold text-[#1e1e1e]">
                    {item.score}%
                  </span>
                </div>
                <div
                  className="relative w-full rounded-lg overflow-hidden h-20 md:h-32"
                  style={{ backgroundColor: '#F3F3F3' }}
                >
                  <div
                    className="absolute bottom-0 w-full transition-all duration-300"
                    style={{
                      height: `${heightPercentage}%`,
                      backgroundColor: item.color,
                      minHeight: '20px'
                    }}
                  />
                </div>
                <span className="text-xs font-medium text-[#1e1e1e]">{item.trait}</span>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
