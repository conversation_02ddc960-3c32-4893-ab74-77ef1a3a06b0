"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "../ui/card"

export function ResponsiveTest() {
  const [screenSize, setScreenSize] = useState<string>("")
  const [windowSize, setWindowSize] = useState<{ width: number; height: number }>({
    width: 0,
    height: 0
  })

  useEffect(() => {
    const updateSize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setWindowSize({ width, height })
      
      if (width < 640) {
        setScreenSize("Mobile (< 640px)")
      } else if (width < 768) {
        setScreenSize("Small Tablet (640px - 768px)")
      } else if (width < 1024) {
        setScreenSize("Tablet (768px - 1024px)")
      } else if (width < 1280) {
        setScreenSize("Desktop (1024px - 1280px)")
      } else {
        setScreenSize("Large Desktop (> 1280px)")
      }
    }

    updateSize()
    window.addEventListener('resize', updateSize)
    
    return () => window.removeEventListener('resize', updateSize)
  }, [])

  return (
    <Card className="bg-white border-[#eaecf0] mb-4">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-[#1e1e1e]">
          Responsive Test Panel
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p className="text-sm">
            <strong>Screen Size:</strong> {screenSize}
          </p>
          <p className="text-sm">
            <strong>Window Dimensions:</strong> {windowSize.width} x {windowSize.height}px
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-4">
            <div className="bg-blue-100 p-2 rounded text-center text-xs">
              <div className="block sm:hidden">📱 Mobile</div>
              <div className="hidden sm:block md:hidden">📱 SM</div>
              <div className="hidden md:block lg:hidden">💻 MD</div>
              <div className="hidden lg:block xl:hidden">🖥️ LG</div>
              <div className="hidden xl:block">🖥️ XL</div>
            </div>
            <div className="bg-green-100 p-2 rounded text-center text-xs">
              Cols: 2→4
            </div>
            <div className="bg-yellow-100 p-2 rounded text-center text-xs">
              Gap: 3→4
            </div>
            <div className="bg-purple-100 p-2 rounded text-center text-xs">
              Padding: 4→6
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
