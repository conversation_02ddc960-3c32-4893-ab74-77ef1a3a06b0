import { Card, CardContent } from "../ui/card"
import type { StatCard } from "../../types/dashboard"

interface StatsCardProps {
  stat: StatCard
}

export function StatsCard({ stat }: StatsCardProps) {
  return (
    <Card className="rounded-2xl border text-card-foreground shadow-sm bg-white border-[#eaecf0]">
      <CardContent className="p-3 md:p-4">
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <div className="text-2xl md:text-3xl font-bold text-[#1e1e1e] truncate">{stat.value}</div>
            <div className="text-xs text-[#64707d] line-clamp-2">{stat.label}</div>
          </div>
          <div
            className="w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center flex-shrink-0 ml-2"
            style={{ backgroundColor: stat.color }}
          >
            <img
              src={`/icons/${stat.icon}`}
              alt={stat.label}
              className="w-5 h-5 md:w-6 md:h-6"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
