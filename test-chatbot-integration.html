<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot API Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Chatbot API Integration Test</h1>
        <p>This page tests the chatbot API integration with https://api.chhrone.web.id/</p>

        <div class="test-section info">
            <h3>📋 Configuration</h3>
            <label>API Base URL:</label>
            <input type="text" id="apiBaseUrl" value="https://api.chhrone.web.id" readonly>
            
            <label>Bearer Token:</label>
            <input type="text" id="bearerToken" placeholder="Enter your JWT token here">
            
            <label>Assessment ID:</label>
            <input type="text" id="assessmentId" placeholder="Enter assessment ID">
            
            <button onclick="saveConfig()">Save Configuration</button>
        </div>

        <div class="test-section">
            <h3>🔍 Test Results</h3>
            <button onclick="testHealthCheck()">1. Test Health Check</button>
            <button onclick="testCreateConversation()">2. Test Create Conversation</button>
            <button onclick="testSendMessage()" id="sendMessageBtn" disabled>3. Test Send Message</button>
            <button onclick="clearLogs()">Clear Logs</button>
            
            <div id="testResults" class="log">Ready to test...\n</div>
        </div>

        <div class="test-section">
            <h3>💬 Live Chat Test</h3>
            <input type="text" id="chatMessage" placeholder="Type a message to test...">
            <button onclick="sendTestMessage()">Send Message</button>
            <div id="chatLog" class="log">Chat messages will appear here...\n</div>
        </div>
    </div>

    <script>
        let currentConversationId = null;
        let apiConfig = {
            baseUrl: 'https://api.chhrone.web.id',
            token: '',
            assessmentId: ''
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testResults');
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function chatLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const chatElement = document.getElementById('chatLog');
            chatElement.textContent += `[${timestamp}] ${message}\n`;
            chatElement.scrollTop = chatElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('testResults').textContent = 'Logs cleared...\n';
            document.getElementById('chatLog').textContent = 'Chat cleared...\n';
        }

        function saveConfig() {
            apiConfig.token = document.getElementById('bearerToken').value;
            apiConfig.assessmentId = document.getElementById('assessmentId').value;
            
            if (apiConfig.token && apiConfig.assessmentId) {
                log('Configuration saved successfully', 'success');
            } else {
                log('Please fill in both Bearer Token and Assessment ID', 'error');
            }
        }

        async function makeApiCall(endpoint, method = 'GET', body = null) {
            const url = `${apiConfig.baseUrl}${endpoint}`;
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiConfig.token}`
                }
            };

            if (body) {
                options.body = JSON.stringify(body);
            }

            log(`Making ${method} request to: ${url}`);
            
            try {
                const response = await fetch(url, options);
                const data = await response.json();
                
                if (response.ok) {
                    log(`API call successful (${response.status})`, 'success');
                    return { success: true, data, status: response.status };
                } else {
                    log(`API call failed (${response.status}): ${JSON.stringify(data)}`, 'error');
                    return { success: false, data, status: response.status };
                }
            } catch (error) {
                log(`Network error: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function testHealthCheck() {
            log('Testing chatbot health check...');
            const result = await makeApiCall('/api/chatbot/health');
            
            if (result.success) {
                log(`Health check passed: ${result.data.status}`, 'success');
            } else {
                log('Health check failed', 'error');
            }
        }

        async function testCreateConversation() {
            if (!apiConfig.token || !apiConfig.assessmentId) {
                log('Please configure Bearer Token and Assessment ID first', 'error');
                return;
            }

            log('Testing conversation creation...');
            const result = await makeApiCall('/api/chatbot/assessment/from-assessment', 'POST', {
                assessment_id: apiConfig.assessmentId,
                conversation_type: 'career_guidance',
                include_suggestions: true
            });

            if (result.success && result.data.success) {
                currentConversationId = result.data.data.conversationId;
                log(`Conversation created: ${currentConversationId}`, 'success');
                log(`Welcome message: ${result.data.data.personalizedWelcome?.content?.substring(0, 100)}...`, 'success');
                document.getElementById('sendMessageBtn').disabled = false;
            } else {
                log('Failed to create conversation', 'error');
            }
        }

        async function testSendMessage() {
            if (!currentConversationId) {
                log('Please create a conversation first', 'error');
                return;
            }

            log('Testing message sending...');
            const result = await makeApiCall(`/api/chatbot/conversations/${currentConversationId}/messages`, 'POST', {
                content: 'What career paths would be best suited for my personality type?',
                type: 'text'
            });

            if (result.success && result.data.success) {
                const aiResponse = result.data.data.aiResponse;
                log(`Message sent successfully`, 'success');
                log(`AI Response: ${aiResponse.content.substring(0, 150)}...`, 'success');
            } else {
                log('Failed to send message', 'error');
            }
        }

        async function sendTestMessage() {
            const messageInput = document.getElementById('chatMessage');
            const message = messageInput.value.trim();
            
            if (!message) {
                chatLog('Please enter a message');
                return;
            }

            if (!currentConversationId) {
                chatLog('Please create a conversation first using the test buttons above');
                return;
            }

            chatLog(`You: ${message}`);
            messageInput.value = '';

            const result = await makeApiCall(`/api/chatbot/conversations/${currentConversationId}/messages`, 'POST', {
                content: message,
                type: 'text'
            });

            if (result.success && result.data.success) {
                const aiResponse = result.data.data.aiResponse;
                chatLog(`AI: ${aiResponse.content}`);
            } else {
                chatLog(`Error: Failed to send message - ${JSON.stringify(result.data || result.error)}`);
            }
        }

        // Allow Enter key to send messages
        document.getElementById('chatMessage').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendTestMessage();
            }
        });

        log('Test page loaded. Please configure your API credentials and start testing.');
    </script>
</body>
</html>
